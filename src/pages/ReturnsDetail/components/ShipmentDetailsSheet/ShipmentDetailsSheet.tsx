import { Scrollable, Sheet } from '@shopify/polaris';
import { useFeatureAvailable } from 'aftershipBillingUi/BillingSDK';
import {
    ModalIdMap,
    useBillingModalController,
} from 'context/billingModalController/billingModalController';
import { useReturnsTrackingTurnOnModal } from 'pages/ReturnTracking/hooks/useReturnsTrackingTurnOnModal';
import React, { useEffect, useRef } from 'react';
import { useQueryAfterShipDetail } from 'resources/returnsDetail/useAfterShipDetail';
import FeatureCode from 'types/featureCode';

import OrderSection from './components/OrderSection/OrderSection';
import OverviewSection from './components/OverviewSection';
import ShipmentDetailsTitle from './components/ShipmentDetailsTitle/ShipmentDetailsTitle';
import TimelineSection from './components/TimelineSection/TimelineSection';
import { useShipmentDetailsPermission } from './hooks/useShipmentDetailsPermission';
import style from './ShipmentDetailsSheet.module.scss';

interface ShipmentDetailsSheetProps {
    open: boolean;
    onClose: () => void;
    returnId: string;
    shipmentId?: string;
    trackingRefetch?: VoidFunction;
    fallbackShipment?: {
        trackingNumber?: string;
        trackingSlug?: string;
        courierSlug: string;
    };
}

const ShipmentDetailsSheet: React.FC<ShipmentDetailsSheetProps> = ({
    open,
    onClose,
    returnId,
    shipmentId,
    fallbackShipment,
    trackingRefetch = () => {},
}) => {
    const divRef = useRef<HTMLDivElement>(null);

    const { data: trackingDetail } = useQueryAfterShipDetail({
        returnId: returnId,
        shipmentId: shipmentId,
    });

    // 权限检测
    const permission = useShipmentDetailsPermission();

    // 开启 returns tracking 功能的弹窗
    const {
        modal: returnsTrackingModal,
        onOpen: openReturnsTrackingModal,
    } = useReturnsTrackingTurnOnModal();

    // billingModalController
    const { openBillingModal } = useBillingModalController();

    // Multi-mile hand-over 特性开关
    const { available: hasMultiMileFeature } = useFeatureAvailable(
        FeatureCode.REVAMP_MULTI_SEGMENTS,
        'aftership'
    );
    useEffect(() => {
        console.log('🔍 hasMultiMileFeature', hasMultiMileFeature);
    }, [hasMultiMileFeature]);
    // 权限检验效果 - 当请求打开但权限不足时，阻止打开并显示对应弹窗
    useEffect(() => {
        if (open && !permission.isLoading && permission.status !== 'allowed') {
            // 阻止原抽屉打开，立即关闭
            onClose();
            // 根据权限状态显示对应弹窗
            switch (permission.status) {
                case 'no_feature_access':
                    openBillingModal(ModalIdMap.UnlockFeature, {
                        featureCode: FeatureCode.RETURN_TRACKING,
                    });
                    break;
                case 'returns_service_disabled':
                    openReturnsTrackingModal();
                    break;
                default:
                    break;
            }
        }
    }, [
        open,
        permission.status,
        permission.isLoading,
        openReturnsTrackingModal,
        openBillingModal,
        onClose,
    ]);

    useEffect(() => {
        if (divRef.current && open) {
            const sheetContainer = divRef.current.closest(
                '.Polaris-Sheet__Container'
            ) as HTMLDivElement;
            const dialogNode = divRef.current.closest(
                '.Polaris-Sheet'
            ) as HTMLDivElement;
            const maskLayer = sheetContainer?.nextSibling as HTMLDivElement;

            if (sheetContainer) {
                sheetContainer.classList.add('shipmentDetailsSheetContainer');
            }
            if (dialogNode) {
                dialogNode.classList.add('shipmentDetailsSheet');
            }

            if (document.documentElement.clientWidth < 480) {
                if (sheetContainer) {
                    sheetContainer.style.maxWidth = '100%';
                }
                if (dialogNode) {
                    dialogNode.style.maxWidth = '100%';
                }
            } else {
                if (sheetContainer) {
                    sheetContainer.style.width = '504px';
                }
                if (dialogNode) {
                    dialogNode.style.width = '504px';
                }
            }

            if (sheetContainer) {
                sheetContainer.style.zIndex = '521';
            }
            if (maskLayer) {
                maskLayer.style.backgroundColor = 'rgba(33, 43, 54, 0.3)';
            }
        }
    }, [open]);

    // 渲染内容 - 只有权限允许时才会调用此函数
    const renderContent = () => {
        // 检查是否有足够的跟踪数据来显示 Overview 和 Timeline 部分
        const hasTrackingData =
            trackingDetail &&
            (trackingDetail.status ||
                trackingDetail.checkpoints?.length ||
                trackingDetail.shipmentPickupDate ||
                trackingDetail.shipmentDeliveryDate ||
                trackingDetail.originAddress ||
                trackingDetail.destinationAddress);

        return (
            <>
                <ShipmentDetailsTitle
                    trackingDetail={trackingDetail}
                    onClose={onClose}
                    fallbackShipment={fallbackShipment}
                />

                <div className={style.scrollable}>
                    <Scrollable style={{ height: '100%', padding: '12px' }}>
                        {hasTrackingData && (
                            <div className={style.card}>
                                <OverviewSection
                                    trackingDetail={trackingDetail}
                                    showMultiMileHO={
                                        hasMultiMileFeature &&
                                        Boolean(
                                            trackingDetail
                                                ?.multipleFulfilledCarriers
                                                ?.handedOverAt
                                        )
                                    }
                                />
                            </div>
                        )}

                        {hasTrackingData && (
                            <div className={style.card}>
                                <TimelineSection
                                    trackingDetail={trackingDetail}
                                />
                            </div>
                        )}

                        <div className={style.card}>
                            <OrderSection
                                trackingDetail={trackingDetail}
                                trackingRefetch={trackingRefetch}
                            />
                        </div>
                    </Scrollable>
                </div>
            </>
        );
    };

    return (
        <>
            <Sheet
                open={open && permission.status === 'allowed'}
                onClose={onClose}
                accessibilityLabel="shipmentDetailsSheet"
            >
                <div ref={divRef} />
                <div
                    className={style.shipmentDetailsSheet}
                    id="shipment-detail-sheet"
                >
                    {renderContent()}
                </div>
            </Sheet>

            {/* Returns tracking 开启弹窗 */}
            {returnsTrackingModal}
        </>
    );
};

export default ShipmentDetailsSheet;
